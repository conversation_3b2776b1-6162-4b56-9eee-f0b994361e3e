import {motion} from 'motion/react';
import React from 'react';
import INFO from '../../data/user.js';
import {FontAwesomeIcon} from '@fortawesome/react-fontawesome';
import {faEnvelope} from '@fortawesome/free-solid-svg-icons';
import './styles/socials.css';
import Footer from '../../components/common/footer.jsx';

function ContactWithFooter() {
	return (
		<React.Fragment>
			<section id="contact" className="scroll-child contact-with-footer">
				<div className="contact-content">
					<motion.div
						initial={{opacity: 0}}
						whileInView={{opacity: 1}}
						transition={{duration: 0.75}}
						className="homepage-container bg-blur" style={{ width: '85%', maxWidth: '1200px', margin: '0 auto' }}>
						<div className="title contact-title">
							<h2>
								Contact
							</h2>
						</div>

						<div className="subtitle contact-subtitle">
							<h4>
								Thank you for your interest in getting in touch with me.
								<br/>
								Please feel free to email me directly at
							</h4>
						</div>
						<div className="socials-container">
							<div className="contact-socials">
								<div className="socials">
									<div className="email-wrapper">
										<a
											href={`mailto:${INFO.main.email}`}
											target="_blank"
											rel="noreferrer"
										>
											<div className="social-icon">
												<FontAwesomeIcon icon={faEnvelope}/>
											</div>

											<div className="social-text link-text">{INFO.main.email}</div>
										</a>
									</div>
								</div>
							</div>
						</div>
					</motion.div>
				</div>
				<div className="footer-wrapper">
					<Footer />
				</div>
			</section>
		</React.Fragment>
	);
}

export default ContactWithFooter;
