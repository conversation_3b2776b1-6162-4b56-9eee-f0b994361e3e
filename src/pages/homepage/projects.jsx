import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import React, { useState, useEffect } from 'react';
import Carousel from 'react-material-ui-carousel';
import Project from '../../components/projects/project.jsx';
import '../styles/carousel.css';

function Projects() {
	const [activeIndex, setActiveIndex] = useState(0);

	// Update active indicator styling when carousel changes
	useEffect(() => {
		const updateActiveIndicator = () => {
			const indicators = document.querySelectorAll('.carousel-indicators .MuiButton-root');

			// Clear all active states first
			indicators.forEach((indicator) => {
				indicator.classList.remove('carousel-active', 'active', 'Mui-selected', 'MuiButton-contained');
				indicator.setAttribute('aria-selected', 'false');
				indicator.setAttribute('aria-current', 'false');
				indicator.removeAttribute('data-active');
			});

			// Set active state for current index
			if (indicators[activeIndex]) {
				const activeIndicator = indicators[activeIndex];
				activeIndicator.classList.add('carousel-active', 'active', 'Mui-selected');
				activeIndicator.setAttribute('aria-selected', 'true');
				activeIndicator.setAttribute('aria-current', 'true');
				activeIndicator.setAttribute('data-active', 'true');
			}
		};

		// Use requestAnimationFrame for better performance
		const rafId = requestAnimationFrame(() => {
			setTimeout(updateActiveIndicator, 100);
		});

		return () => {
			cancelAnimationFrame(rafId);
		};
	}, [activeIndex]);

	const handleCarouselChange = (index, previous) => {
		// Robust index validation and state update
		if (typeof index === 'number' && index >= 0 && index !== activeIndex) {
			setActiveIndex(index);
		}
	};

	return (
		<React.Fragment>
			<section id="projects" className="scroll-child">
				<div className="white-space"></div>
				<motion.div initial={{opacity: 0}}
				            whileInView={{opacity: 1}}
				            transition={{duration: 0.75}}
				            className="homepage-container bg-blur">
					<div className="title projects-title">
						<h2>Skills</h2>
					</div>

					<div className="projects-list" role="region" aria-label="Skills carousel">
						<Carousel
							navButtonsAlwaysVisible={true}
							navButtonsProps={{
								className: "carousel-nav-btn",
								style: {
									backgroundColor: "rgba(197, 197, 197, 0.7)",
									color: "#27272a",
									padding: 0
								}
							}}
							navButtonsWrapperProps={{
								className: "nav-buttons-wrapper"
							}}
							indicatorContainerProps={{
								className: "carousel-indicators"
							}}
							indicatorIconButtonProps={{
								style: {
									padding: '6px',
									margin: '6px 4px'
								},
								className: "carousel-indicator-btn"
							}}
							className="skills-carousel"
							animation="slide"
							autoPlay={false}
							interval={6000}
							fullHeightHover={false}
							onChange={handleCarouselChange}
							index={activeIndex}
							indicators={true}
							cycleNavigation={true}
							swipe={true}
							style={{ width: '100%' }}
						>
							{INFO.projects.map((project, index) => (
								<div className="carousel-slide" key={index}>
									<Project
										logo={project.logo}
										title={project.title}
										description={project.description}
									/>
								</div>
							))}
						</Carousel>
					</div>
				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Projects;
