import React, {useEffect, lazy, Suspense} from 'react';
import {Helmet} from 'react-helmet';
import NavBar from '../components/common/navBar';

import INFO from '../data/user';
import SEO from '../data/seo';

import './styles/homepage.css';
import '../App.css'

// Lazy load components
const Projects = lazy(() => import('./homepage/projects.jsx'));
const ContactWithFooter = lazy(() => import('./homepage/contactWithFooter.jsx'));
const Home = lazy(() => import('./homepage/home.jsx'));

// Loading component
const LoadingFallback = () => (
  <div className="loading-container">
    <div className="loading-spinner"></div>
  </div>
);


const Homepage = () => {

	useEffect(() => {
		window.scrollTo(0, 0);
	}, []);


	const currentSEO = SEO.find((item) => item.page === 'home');
	return (
		<React.Fragment>
			<Helmet>
				<title>{INFO.main.title}</title>
				<meta name="description" content={currentSEO.description}/>
				<meta
					name="keywords"
					content={currentSEO.keywords.join(', ')}
				/>
			</Helmet>

			<NavBar active="home"/>
			<div id="content-wrapper" className="content-wrapper">
				<Suspense fallback={<LoadingFallback />}>
					<Home/>
					<Projects/>
					<ContactWithFooter/>
				</Suspense>
			</div>


		</React.Fragment>
	);
};

export default Homepage;
