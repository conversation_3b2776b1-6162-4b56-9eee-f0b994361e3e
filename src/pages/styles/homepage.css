.title {
    color: var(--primary-color);
    text-align: center; /* Ensure titles are centered */
    width: 100%;
}

.subtitle {
    color: var(--secondary-color);
    text-align: center; /* Ensure subtitles are centered */
    width: 100%;
}

/* Contact-specific styling for better mobile centering */
.contact-title {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 1rem;
}

.contact-title h2 {
    text-align: center;
    margin: 0;
    padding: 0;
}

.contact-subtitle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 1.5rem;
}

.contact-subtitle h4 {
    text-align: center;
    margin: 0;
    padding: 0;
    line-height: 1.5;
}

/* Dark mode text styling */
.dark .title {
    color: var(--dark-primary-text);
}

.dark .subtitle {
    color: var(--dark-secondary-text);
}

/* Dark mode homepage container styling */
.dark .homepage-container {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.dark .homepage-container:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.5),
        0 0 25px rgba(209, 213, 219, 0.15);
}

.homepage-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6% 8%;
    width: 85%; /* Consistent with Skills carousel cards */
    max-width: 1200px;
    margin: 0 auto;
    color: rgba(110, 110, 110, 0.46);
    backdrop-filter: blur(20px);
    background: var(--glass-base);
    box-shadow: 0 4px 8px rgba(54, 54, 54, 0.3);
    border-radius: 30px;
    border: 1px solid var(--glass-border);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    position: relative;
    overflow: visible;
    box-sizing: border-box;
}

.homepage-container:hover {
    transform: translateY(-5px);
    background: var(--glass-hover);
    border-color: var(--glass-border-hover);
    box-shadow:
        0 8px 16px rgba(54, 54, 54, 0.4),
        0 0 25px rgba(255, 255, 255, 0.2);
}

/* Tablet styles - Consistent with Skills carousel */
@media (max-width: 1024px) {
    .homepage-container {
        width: 88%; /* Consistent with Skills carousel responsive width */
        max-width: 900px;
        padding: 5% 6%;
        margin: 0 auto;
    }
}

/* Mobile landscape and small tablets - Consistent with Skills carousel */
@media (max-width: 768px) {
    .homepage-container {
        width: 90%; /* Consistent with Skills carousel responsive width */
        max-width: 700px;
        padding: 5% 6%;
        margin: 0 auto;
        overflow: visible !important;
    }
}

/* Mobile portrait - Consistent with Skills carousel */
@media (max-width: 480px) {
    .homepage-container {
        width: 92%; /* Consistent with Skills carousel responsive width */
        max-width: 400px;
        padding: 4% 5%;
        margin: 0 auto;
        border-radius: 20px;
    }
}

/* Very small screens - Consistent with Skills carousel */
@media (max-width: 380px) {
    .homepage-container {
        width: 95%; /* Consistent with Skills carousel responsive width */
        padding: 4% 4%;
        margin: 0 auto;
        border-radius: 15px;
    }

    /* Enhanced very small screen contact text centering */
    .contact-title h2 {
        font-size: 1.6rem;
        text-align: center;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .contact-subtitle h4 {
        font-size: 1rem;
        text-align: center;
        line-height: 1.4;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .contact-subtitle {
        margin-bottom: 0.8rem;
    }
}

.homepage-socials {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 15px;
}

.homepage-social-icon {
    height: 25px;
    margin: 0 5px;
    transition: transform var(--transition-fast), color var(--transition-fast);
    font-size: 1.5rem;
}

.homepage-social-icon {
    color: var(--icon-default);
}

.homepage-social-icon:hover {
    transform: scale(1.2);
    color: var(--icon-hover);
}

/* Dark mode homepage styling */
.dark .homepage-social-icon {
    color: var(--dark-icon-default);
}

.dark .homepage-social-icon:hover {
    color: var(--dark-icon-hover);
}

.bg-blur {
    backdrop-filter: blur(10px);
}

.homepage-first-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.homepage-first-area-left-side {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
}

@media (max-width: 480px) {
    .homepage-title h2 {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .homepage-subtitle h4 {
        font-size: 1.1rem;
        text-align: center;
        line-height: 1.4;
    }

    .homepage-social-icon {
        font-size: 1.3rem;
    }

    /* Enhanced mobile contact text centering */
    .contact-title h2 {
        font-size: 1.8rem;
        text-align: center;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .contact-subtitle h4 {
        font-size: 1.1rem;
        text-align: center;
        line-height: 1.4;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .contact-subtitle {
        margin-bottom: 1rem;
    }
}
