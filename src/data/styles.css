:root {
    /* ------- Enhanced Base Colors for WCAG Compliance ------- */
    --primary-color: #1f1f23; /* Darker for better contrast (4.5:1 ratio) */
    --secondary-color: #52525b; /* Enhanced contrast while maintaining monochromatic scheme */
    --tertiary-color: #a1a1aa; /* Improved readability */
    --quaternary-color: #e4e4e7;
    --link-color: #71717a;
    --accent-color: #f4f4f5;

    /* ------- Enhanced Glassmorphism Colors for Professional Appearance ------- */
    --glass-base: rgba(248, 250, 252, 0.28); /* Slightly increased opacity for better visibility */
    --glass-hover: rgba(248, 250, 252, 0.38); /* Enhanced hover state */
    --glass-active: rgba(248, 250, 252, 0.48); /* More pronounced active state */
    --glass-border: rgba(255, 255, 255, 0.35); /* Improved border visibility */
    --glass-border-hover: rgba(255, 255, 255, 0.45); /* Enhanced hover border */

    /* ------- Progress Bar Colors ------- */
    --progress-primary: #f4f4f5;
    --progress-secondary: #d4d4d8;
    --progress-tertiary: #a1a1aa;
    --progress-quaternary: #71717a;
    --progress-glow: rgba(244, 244, 245, 0.6);
    --progress-glow-secondary: rgba(212, 212, 216, 0.4);

    /* ------- Interactive States ------- */
    --hover-overlay: rgba(255, 255, 255, 0.15);
    --active-overlay: rgba(255, 255, 255, 0.25);
    --focus-ring: rgba(244, 244, 245, 0.6);

    /* ------- Enhanced Icon Colors for Better Accessibility ------- */
    --icon-default: #6b7280; /* Improved contrast while maintaining monochromatic scheme */
    --icon-hover: #1f2937; /* Enhanced hover state with better contrast */
    --icon-active: #4b5563; /* More pronounced active state */

    /* ------- Additional Accent Colors ------- */
    --success-color: #d4d4d8;
    --warning-color: #a1a1aa;
    --error-color: #71717a;
    --info-color: #e4e4e7;

    /* ------- Modern Dark Mode Colors for Professional Appearance ------- */
    --dark-glass-base: rgba(30, 41, 59, 0.65); /* Rich slate with better opacity */
    --dark-glass-hover: rgba(51, 65, 85, 0.75); /* Enhanced hover with warmer tone */
    --dark-glass-active: rgba(71, 85, 105, 0.85); /* More pronounced active state */
    --dark-glass-border: rgba(148, 163, 184, 0.4); /* Improved border visibility */
    --dark-glass-border-hover: rgba(203, 213, 225, 0.5); /* Brighter hover border */
    --dark-hover-overlay: rgba(255, 255, 255, 0.12); /* Subtle overlay */
    --dark-active-overlay: rgba(255, 255, 255, 0.2); /* Enhanced active state */

    /* ------- Modern Dark Mode Icon Colors for Better Accessibility ------- */
    --dark-icon-default: #cbd5e1; /* Brighter default for better visibility */
    --dark-icon-hover: #f1f5f9; /* Clean white hover state */
    --dark-icon-active: #94a3b8; /* Balanced active state */

    /* ------- Modern Dark Mode Text Colors for WCAG Compliance ------- */
    --dark-primary-text: #f8fafc; /* Pure white for maximum contrast */
    --dark-secondary-text: #e2e8f0; /* Bright secondary text */
    --dark-tertiary-text: #cbd5e1; /* Clear tertiary text */
    --dark-muted-text: #94a3b8; /* Readable muted text */
    /* ----------------------------------- */

    /* ------- fonts ------- */
    --primary-font: "Poppins", sans-serif;
    --secondary-font: "Montserrat", sans-serif;
    --heading-weight: 600;
    --body-weight: 400;
    --heading-line-height: 1.2;
    --body-line-height: 1.6;
    /* --------------------- */

    /* ------- spacing ------- */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    /* ----------------------- */

    /* ------- transitions ------- */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    /* --------------------------- */
}
