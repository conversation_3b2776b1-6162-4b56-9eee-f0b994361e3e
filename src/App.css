html {
    overflow-y: hidden; /* Let content-wrapper handle scrolling */
    overflow-x: hidden;
    color: #1f1f23;
    background:
        /* Professional micro-texture pattern */
        radial-gradient(circle at 25% 25%, rgba(203, 213, 225, 0.3) 0.8px, transparent 0.8px),
        radial-gradient(circle at 75% 75%, rgba(226, 232, 240, 0.2) 0.6px, transparent 0.6px),
        /* Sophisticated geometric grid */
        linear-gradient(90deg, rgba(241, 245, 249, 0.4) 1px, transparent 1px),
        linear-gradient(180deg, rgba(248, 250, 252, 0.3) 1px, transparent 1px),
        /* Layered depth gradients for visual hierarchy */
        radial-gradient(ellipse 120% 80% at 50% 0%, rgba(255, 255, 255, 0.9) 0%, transparent 50%),
        radial-gradient(ellipse 120% 80% at 50% 100%, rgba(248, 250, 252, 0.8) 0%, transparent 50%),
        /* Professional vertical depth bands */
        linear-gradient(180deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.8) 20%,
            rgba(241, 245, 249, 0.6) 40%,
            rgba(226, 232, 240, 0.4) 60%,
            rgba(203, 213, 225, 0.3) 80%,
            rgba(148, 163, 184, 0.2) 100%
        ),
        /* Sophisticated base foundation */
        linear-gradient(180deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    background-size:
        48px 48px,
        64px 64px,
        32px 32px,
        32px 32px,
        100% 60%,
        100% 60%,
        100% 100%,
        100% 100%;
    background-position:
        0 0,
        24px 24px,
        0 0,
        0 0,
        50% 0%,
        50% 100%,
        0% 0%,
        0% 0%;
    background-attachment: fixed;
    background-repeat: repeat, repeat, repeat, repeat, no-repeat, no-repeat, no-repeat, no-repeat;
    width: 100%;
    max-width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    transition: background-position 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent body scrolling */
    flex-direction: column;
    height: 100vh;
    position: fixed; /* Prevent iOS bounce scrolling */
}

/* Refined professional gradient animations */
@keyframes subtleShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gentleFlow {
    0% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Dark mode gradient animations */
@keyframes subtleShiftDark {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gentleFlowDark {
    0% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

/* Performance optimizations and accessibility */
@media (prefers-reduced-motion: reduce) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation: none !important;
        background-position: 0% 50% !important;
        background-attachment: scroll !important; /* Disable parallax for reduced motion */
        transition: none !important; /* Disable scroll-based background transitions */
    }
}

/* GPU acceleration for smooth animations */
html,
.App {
    will-change: background-position;
    transform: translateZ(0);
    backface-visibility: hidden;
}

@keyframes pulse {
    /* Kept for compatibility */
}

.scroll-child {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    align-content: center;
    scroll-snap-align: start;
    height: 100vh;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 90px 2% 0 2%; /* Top padding for navbar */
    overflow-x: hidden;
    scroll-snap-stop: always; /* Force snap on each section */
}

.contact-with-footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100svh;
    padding: 0;
    width: 100%;
    margin: 0 auto;
}

.contact-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.footer-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 30px;
    margin-top: auto;
    min-height: 120px;
    position: relative;
}

.white-space {
}

/* Loading spinner */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.content-wrapper {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    color: rgba(36,59,8,0);
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    scroll-padding-top: 0; /* Handled by section padding */
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.content-wrapper::-webkit-scrollbar {
    display: none;
}

.title {
    color: #252525;
}

.subtitle {
    color: rgb(47, 47, 47);
}


.App {
    background:
        /* Professional glassmorphism texture overlay */
        radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 1px, transparent 1px),
        radial-gradient(circle at 70% 30%, rgba(248, 250, 252, 0.3) 0.8px, transparent 0.8px),
        /* Sophisticated depth layers for glassmorphism enhancement */
        linear-gradient(180deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 25%, transparent 50%),
        linear-gradient(0deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.2) 30%, transparent 60%),
        /* Professional vertical overlay with enhanced depth */
        linear-gradient(180deg,
            rgba(255, 255, 255, 0.5) 0%,
            rgba(248, 250, 252, 0.4) 15%,
            rgba(241, 245, 249, 0.3) 30%,
            rgba(226, 232, 240, 0.2) 50%,
            rgba(203, 213, 225, 0.3) 70%,
            rgba(148, 163, 184, 0.4) 85%,
            rgba(100, 116, 139, 0.5) 100%
        );
    background-size:
        40px 40px,
        56px 56px,
        100% 70%,
        100% 80%,
        100% 100%;
    background-position:
        0 0,
        20px 20px,
        0% 0%,
        0% 100%,
        0% 0%;
    background-attachment: fixed;
    background-repeat: repeat, repeat, no-repeat, no-repeat, no-repeat;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    padding: 0;
    margin: 0;
    position: relative;
    transition: background-position 0.08s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode (Applied via class) */

/* Dark mode background - Sophisticated dark grey/silver theme */
.dark html {
    background:
        /* Professional dark micro-texture pattern */
        radial-gradient(circle at 25% 25%, rgba(156, 163, 175, 0.15) 0.8px, transparent 0.8px),
        radial-gradient(circle at 75% 75%, rgba(209, 213, 219, 0.08) 0.6px, transparent 0.6px),
        /* Sophisticated dark geometric grid */
        linear-gradient(90deg, rgba(75, 85, 99, 0.2) 1px, transparent 1px),
        linear-gradient(180deg, rgba(107, 114, 128, 0.15) 1px, transparent 1px),
        /* Layered dark depth gradients for visual hierarchy */
        radial-gradient(ellipse 120% 80% at 50% 0%, rgba(31, 41, 55, 0.8) 0%, transparent 50%),
        radial-gradient(ellipse 120% 80% at 50% 100%, rgba(55, 65, 81, 0.7) 0%, transparent 50%),
        /* Professional dark vertical depth bands */
        linear-gradient(180deg,
            rgba(17, 24, 39, 0.98) 0%,
            rgba(31, 41, 55, 0.9) 20%,
            rgba(55, 65, 81, 0.8) 40%,
            rgba(75, 85, 99, 0.7) 60%,
            rgba(107, 114, 128, 0.6) 80%,
            rgba(156, 163, 175, 0.5) 100%
        ),
        /* Sophisticated dark base foundation */
        linear-gradient(180deg, #0f172a 0%, #1f2937 50%, #374151 100%);
    background-size:
        48px 48px,
        64px 64px,
        32px 32px,
        32px 32px,
        100% 60%,
        100% 60%,
        100% 100%,
        100% 100%;
    background-position:
        0 0,
        24px 24px,
        0 0,
        0 0,
        50% 0%,
        50% 100%,
        0% 0%,
        0% 0%;
    background-attachment: fixed;
    background-repeat: repeat, repeat, repeat, repeat, no-repeat, no-repeat, no-repeat, no-repeat;
    transition: background-position 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .App {
    background:
        /* Professional dark texture overlay */
        radial-gradient(circle at 50% 50%, rgba(156, 163, 175, 0.1) 1px, transparent 1px),
        /* Vertical dark glassmorphism layers */
        linear-gradient(180deg, rgba(31, 41, 55, 0.4) 0%, transparent 30%),
        linear-gradient(0deg, rgba(55, 65, 81, 0.3) 0%, transparent 25%),
        /* Sophisticated dark vertical overlay */
        linear-gradient(180deg,
            rgba(17, 24, 39, 0.6) 0%,
            rgba(31, 41, 55, 0.5) 20%,
            rgba(55, 65, 81, 0.4) 40%,
            rgba(75, 85, 99, 0.5) 60%,
            rgba(107, 114, 128, 0.6) 80%,
            rgba(156, 163, 175, 0.7) 100%
        );
    background-size:
        32px 32px,
        100% 50%,
        100% 50%,
        100% 100%;
    background-position:
        0 0,
        0% 0%,
        0% 100%,
        0% 0%;
    background-attachment: fixed;
    background-repeat: repeat, no-repeat, no-repeat, no-repeat;
    transition: background-position 0.08s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scroll-based parallax effects - Vertical movement only */
.content-wrapper {
    /* Enable smooth scrolling for parallax */
    scroll-behavior: smooth;
}

/* Parallax effect on scroll - Light mode */
html {
    /* Subtle vertical parallax movement */
    background-position:
        0 calc(var(--scroll-y, 0) * 0.1px),
        0% calc(var(--scroll-y, 0) * -0.05px),
        0% calc(100% + var(--scroll-y, 0) * 0.03px),
        0% calc(var(--scroll-y, 0) * -0.02px),
        0% 0%;
}

/* Parallax effect on scroll - Dark mode */
.dark html {
    background-position:
        0 calc(var(--scroll-y, 0) * 0.1px),
        0% calc(var(--scroll-y, 0) * -0.05px),
        0% calc(100% + var(--scroll-y, 0) * 0.03px),
        0% calc(var(--scroll-y, 0) * -0.02px),
        0% 0%;
}

/* App overlay parallax - Light mode */
.App {
    background-position:
        0 calc(var(--scroll-y, 0) * 0.05px),
        0% calc(var(--scroll-y, 0) * -0.03px),
        0% calc(100% + var(--scroll-y, 0) * 0.02px),
        0% calc(var(--scroll-y, 0) * -0.01px);
}

/* App overlay parallax - Dark mode */
.dark .App {
    background-position:
        0 calc(var(--scroll-y, 0) * 0.05px),
        0% calc(var(--scroll-y, 0) * -0.03px),
        0% calc(100% + var(--scroll-y, 0) * 0.02px),
        0% calc(var(--scroll-y, 0) * -0.01px);
}

.dark .homepage-container {
    background: var(--dark-glass-base);
    border-color: var(--dark-glass-border);
}

.dark .homepage-container:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    box-shadow:
        0 8px 16px rgba(54, 54, 54, 0.4),
        0 0 20px rgba(255, 255, 255, 0.1);
}

.dark .link-text {
    color: var(--dark-primary-text);
}

.dark .title {
    color: var(--dark-primary-text);
}

.dark .subtitle {
    color: var(--dark-secondary-text);
}

/* Enhanced dark mode text hierarchy */
.dark h1, .dark h2, .dark h3 {
    color: var(--dark-primary-text);
}

.dark h4, .dark h5, .dark h6 {
    color: var(--dark-secondary-text);
}

.dark p, .dark span {
    color: var(--dark-secondary-text);
}

.dark .muted {
    color: var(--dark-tertiary-text);
}

/* Comprehensive dark mode text and element styling */
.dark input,
.dark textarea,
.dark select {
    background: var(--dark-glass-base);
    border: 1px solid var(--dark-glass-border);
    color: var(--dark-primary-text);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    border-color: var(--dark-glass-border-hover);
    outline: 2px solid var(--focus-ring);
}

.dark button {
    background: var(--dark-glass-base);
    border: 1px solid var(--dark-glass-border);
    color: var(--dark-secondary-text);
}

.dark button:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
    color: var(--dark-primary-text);
}

.dark a {
    color: var(--dark-secondary-text);
}

.dark a:hover {
    color: var(--dark-primary-text);
}

/* Dark mode accessibility enhancements */
.dark :focus-visible {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
}

/* Dark mode high contrast support */
@media (prefers-contrast: high) {
    .dark {
        --dark-primary-text: #ffffff;
        --dark-secondary-text: #e0e0e0;
        --dark-tertiary-text: #c0c0c0;
        --dark-glass-border: rgba(255, 255, 255, 0.5);
        --dark-glass-border-hover: rgba(255, 255, 255, 0.7);
    }
}

/* Toggle Switch Style - Legacy (now handled by navbar) */
.dark-mode-toggle {
    display: none; /* Hidden as we now use the navbar toggle */
}

@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}

/* Large screens and tablets */
@media (max-width: 1270px) {
    .content-wrapper {
        width: 100%;
        max-width: 100%;
        padding: 0;
        scroll-snap-type: y mandatory;
        height: 100vh;
    }

    .scroll-child {
        width: 100%;
        max-width: 100vw;
        padding: 80px 3% 0 3%; /* Adjusted for responsive navbar */
        scroll-snap-align: start;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Tablets */
@media (max-width: 1024px) {
    .scroll-child {
        padding: 80px 4% 0 4%;
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .scroll-child {
        padding: 70px 4% 0 4%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }

    .footer-wrapper {
        min-height: 100px;
        padding-bottom: 25px;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .scroll-child {
        padding: 60px 2.5% 0 2.5%;
        width: 100%;
    }

    .content-wrapper {
        width: 100%;
        padding: 0;
    }

    .footer-wrapper {
        min-height: 90px;
        padding-bottom: 20px;
    }
}

/* Very small screens */
@media (max-width: 380px) {
    .scroll-child {
        padding: 55px 1% 0 1%;
        width: 100%;
    }

    .footer-wrapper {
        min-height: 80px;
        padding-bottom: 15px;
    }
}

/* Mobile scroll-snap optimizations */
@media (max-width: 768px) {
    .content-wrapper {
        scroll-snap-type: y mandatory;
        /* Improve touch scrolling on mobile */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
    }

    .scroll-child {
        scroll-snap-stop: always;
    }

    /* Optimize background animations for mobile */
    html,
    .App {
        animation-duration: 280s, 360s;
        background-size: 160% 160%;
        background-attachment: scroll; /* Disable parallax on mobile for performance */
    }

    .dark html,
    .dark .App {
        animation-duration: 300s, 380s;
        background-size: 160% 160%;
        background-attachment: scroll; /* Disable parallax on mobile for performance */
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        /* More aggressive snap on small screens */
        scroll-snap-type: y mandatory;
    }

    /* Further optimize for small screens */
    html,
    .App {
        animation-duration: 300s, 360s;
        background-size: 130% 130%;
        background-attachment: scroll; /* Disable parallax on small screens */
    }

    .dark html,
    .dark .App {
        animation-duration: 320s, 380s;
        background-size: 130% 130%;
        background-attachment: scroll; /* Disable parallax on small screens */
    }
}

/* Very small screens - minimal animation */
@media (max-width: 380px) {
    html,
    .App,
    .dark html,
    .dark .App {
        animation-duration: 400s;
        background-size: 120% 120%;
        background-attachment: scroll; /* Disable parallax on very small screens */
    }
}