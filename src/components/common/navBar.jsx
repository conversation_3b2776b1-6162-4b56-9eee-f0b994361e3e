import React, {useEffect} from 'react';
import {Link, scrollSpy} from 'react-scroll';
import './styles/navBar.css';
import {Divider} from '@mui/material';
import DarkModeSwitch from './darkModeSwitch.jsx';


const NavBar = () => {

	useEffect(() => {
		scrollSpy.update();
	}, []);


	return (
		<React.Fragment>
			<div className="nav-container navbar-slide-down">
				<nav className="navbar">
					<div className="nav-background bg-blur glass-hover">
						<ul className="nav-list">
							<li className="nav-item smooth-transition">
								<Link
									activeClass="active"
									spy={true}
									to="home"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Home</span>
								</Link>
							</li>
							<li className="nav-item smooth-transition">
								<Link
									activeClass="active"
									spy={true}
									to="projects"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Skills</span>
								</Link>
							</li>
							<li className="nav-item smooth-transition">
								<Link
									activeClass="active"
									spy={true}
									to="contact"
									duration={800}
									smooth={true}
									offset={-90}
									containerId="content-wrapper"
								>
									<span className="title">Contact</span>
								</Link>
							</li>
						</ul>
						<Divider orientation="vertical" variant='middle' className='divider' flexItem/>
						<DarkModeSwitch/>
					</div>
				</nav>
			</div>
		</React.Fragment>
	);
};


export default NavBar;
