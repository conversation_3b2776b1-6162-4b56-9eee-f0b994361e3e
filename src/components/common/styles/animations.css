/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn var(--transition-normal) forwards;
}

.slide-up {
    animation: slideUp var(--transition-normal) forwards;
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) forwards;
}

.slide-in-right {
    animation: slideInRight var(--transition-normal) forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Staggered animations */
.stagger-1 {
    animation-delay: 0.1s;
}

.stagger-2 {
    animation-delay: 0.2s;
}

.stagger-3 {
    animation-delay: 0.3s;
}

/* Hover effects */
.hover-lift {
    transition: transform var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.03);
}

.hover-shadow {
    transition: box-shadow var(--transition-fast);
}

.hover-shadow:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Navbar specific animations */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.navbar-slide-down {
    animation: slideDown 0.5s ease-out forwards;
}

/* Progress bar animations */
@keyframes progressGlow {
    0% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
    }
    100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
}

.progress-glow {
    animation: progressGlow 2s ease-in-out infinite;
}

/* Glassmorphism hover effect - Static version */
.glass-hover {
    transition: all var(--transition-normal);
}

.glass-hover:hover {
    backdrop-filter: blur(20px);
    background: rgba(197, 197, 197, 0.4);
    /* Removed translateY to maintain static positioning */
}

/* Focus indicators for accessibility */
.focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.smooth-transition {
    transition: all var(--transition-normal);
}

/* Scroll progress integration utilities */
.scroll-aware {
    transition: all var(--transition-fast);
}

.scroll-aware.scrolling {
    backdrop-filter: blur(25px);
}

/* Performance optimizations for scroll-triggered animations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* Reduced motion support for scroll progress */
@media (prefers-reduced-motion: reduce) {
    .scroll-aware,
    .smooth-transition {
        transition: none;
    }

    .will-change-transform,
    .will-change-opacity {
        will-change: auto;
    }
}
