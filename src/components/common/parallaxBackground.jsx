import React, { useEffect, useCallback, useRef } from 'react';

const ParallaxBackground = () => {
    const rafRef = useRef(null);
    const lastScrollY = useRef(0);

    const updateBackgroundPosition = useCallback(() => {
        // Get the content wrapper element - this is our scroll container
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');

        if (!contentWrapper) {
            return;
        }

        const scrollTop = contentWrapper.scrollTop;
        const scrollHeight = contentWrapper.scrollHeight;
        const clientHeight = contentWrapper.clientHeight;

        // Calculate scroll progress (0 to 1)
        const maxScroll = scrollHeight - clientHeight;
        const scrollProgress = maxScroll > 0 ? Math.min(scrollTop / maxScroll, 1) : 0;

        // Only update if scroll position has changed significantly
        if (Math.abs(scrollTop - lastScrollY.current) < 1) {
            return;
        }

        lastScrollY.current = scrollTop;

        // Enhanced parallax calculations for sophisticated visual experience
        // Smooth easing function for more natural movement
        const easeInOutCubic = (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        const easedProgress = easeInOutCubic(scrollProgress);

        // Light mode: Subtle horizontal movement with refined positioning
        const lightBgPositionX = 20 + (easedProgress * 60); // Move from 20% to 80% for subtle effect
        const lightBgPositionY = 45 + (Math.sin(scrollProgress * Math.PI) * 10); // Gentle wave motion
        const lightBgPosition = `${lightBgPositionX}% ${lightBgPositionY}%`;

        // Dark mode: Enhanced diagonal movement with sophisticated patterns
        const darkBgPositionX = 15 + (easedProgress * 70); // Refined horizontal movement
        const darkBgPositionY = 40 + (easedProgress * 40) + (Math.cos(scrollProgress * Math.PI * 2) * 5); // Complex motion
        const darkBgPosition = `${darkBgPositionX}% ${darkBgPositionY}%`;

        // App layer: Counter-movement with refined parallax depth
        const appBgPositionX = 85 - (easedProgress * 50); // Reduced movement for subtlety
        const appBgPositionY = 30 + (scrollProgress * 40) + (Math.sin(scrollProgress * Math.PI * 1.5) * 8);
        const appBgPosition = `${appBgPositionX}% ${appBgPositionY}%`;

        // Apply to HTML background (base layer)
        const htmlElement = document.documentElement;
        if (htmlElement) {
            htmlElement.style.backgroundPosition = lightBgPosition;
        }

        // Apply to App background (overlay layer)
        const appElement = document.querySelector('.App');
        if (appElement) {
            appElement.style.backgroundPosition = appBgPosition;
        }

        // Handle dark mode backgrounds
        const isDarkMode = document.body.classList.contains('dark');
        if (isDarkMode) {
            if (htmlElement) {
                htmlElement.style.backgroundPosition = darkBgPosition;
            }
        }
    }, []);

    const handleScroll = useCallback(() => {
        // Cancel any pending animation frame
        if (rafRef.current) {
            cancelAnimationFrame(rafRef.current);
        }

        // Schedule the background update for the next frame
        rafRef.current = requestAnimationFrame(updateBackgroundPosition);
    }, [updateBackgroundPosition]);

    useEffect(() => {
        // Get the content wrapper element
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');

        if (!contentWrapper) {
            console.warn('Content wrapper not found for parallax background');
            return;
        }

        // Initial background position
        updateBackgroundPosition();

        // Add scroll listener with passive option for better performance
        contentWrapper.addEventListener('scroll', handleScroll, { passive: true });

        // Handle resize events that might affect scroll calculation
        const handleResize = () => {
            setTimeout(updateBackgroundPosition, 100);
        };
        window.addEventListener('resize', handleResize, { passive: true });

        return () => {
            // Cleanup
            contentWrapper.removeEventListener('scroll', handleScroll);
            window.removeEventListener('resize', handleResize);

            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
        };
    }, [handleScroll, updateBackgroundPosition]);

    // This component doesn't render anything visible
    return null;
};

export default ParallaxBackground;
