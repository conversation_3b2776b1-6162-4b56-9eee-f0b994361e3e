import React, { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import Switch from 'react-switch';
import './styles/darkModeSwitch.css'; // Make sure the path is correct
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMoon, faSun } from '@fortawesome/free-solid-svg-icons';

function DarkModeSwitch() {
	const [isDarkMode, setDarkMode] = useState(() => {
		const storedTheme = localStorage.getItem('darkTheme');
		return storedTheme === 'true';
	});

	const toggleDarkMode = () => {
		setDarkMode(!isDarkMode);
	};

	const isSystemDarkMode = useMediaQuery({
		query: '(prefers-color-scheme: dark)',
	});

	useEffect(() => {
		const newDarkMode = typeof localStorage.getItem('darkTheme') == 'string' ? localStorage.getItem('darkTheme') === 'true' : isSystemDarkMode;
		setDarkMode(newDarkMode);
	}, [isSystemDarkMode]);

	useEffect(() => {
		localStorage.setItem('darkTheme', isDarkMode.toString()); // Store boolean as string
		if (isDarkMode) {
			document.body.classList.add('dark');
		} else {
			document.body.classList.remove('dark');
		}
	}, [isDarkMode]);

	return (
		<React.Fragment>
			<div className="dark-mode-switch-wrapper">
				<Switch
					onColor="rgba(113, 113, 122, 0.8)"
					offColor="rgba(212, 212, 216, 0.6)"
					className="mui-dark-mode-switch"
					checked={isDarkMode}
					onChange={toggleDarkMode}
					offHandleColor="rgba(244, 244, 245, 0.95)"
					onHandleColor="rgba(39, 39, 42, 0.95)"
					checkedIcon={false}
					uncheckedIcon={false}
					uncheckedHandleIcon={
						<FontAwesomeIcon
							className='dark-mode-icons'
							size={'sm'}
							icon={faSun}
						/>
					}
					checkedHandleIcon={
						<FontAwesomeIcon
							className='dark-mode-icons'
							size={'sm'}
							icon={faMoon}
						/>
					}
					height={24}
					width={48}
					handleDiameter={20}
				/>
			</div>
		</React.Fragment>
	);

}

export default DarkModeSwitch;