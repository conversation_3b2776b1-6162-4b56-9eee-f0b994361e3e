import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp } from '@fortawesome/free-solid-svg-icons';
import './styles/scrollProgress.css';

const ScrollProgress = () => {
    const [scrollProgress, setScrollProgress] = useState(0);
    const [showButton, setShowButton] = useState(false);
    const [isScrolling, setIsScrolling] = useState(false);
    const rafRef = useRef(null);
    const scrollTimeoutRef = useRef(null);

    const calculateProgress = useCallback(() => {
        // Get the content wrapper element - this is our scroll container
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');

        if (!contentWrapper) {
            return 0;
        }

        // Use the content wrapper's scroll properties
        const scrollTop = contentWrapper.scrollTop;
        const scrollHeight = contentWrapper.scrollHeight;
        const clientHeight = contentWrapper.clientHeight;

        // Calculate total scrollable height
        const totalScrollHeight = scrollHeight - clientHeight;

        // Ensure we don't go below 0 or above 100
        const progress = totalScrollHeight > 0
            ? Math.max(0, Math.min((scrollTop / totalScrollHeight) * 100, 100))
            : 0;

        return progress;
    }, []);

    const handleScroll = useCallback(() => {
        const progress = calculateProgress();

        // Get scroll position from content wrapper
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');
        const currentScroll = contentWrapper ? contentWrapper.scrollTop : 0;

        setScrollProgress(progress);
        setShowButton(currentScroll > 100); // Show button after scrolling past first section
        setIsScrolling(true);

        // Clear existing timeout
        if (scrollTimeoutRef.current) {
            clearTimeout(scrollTimeoutRef.current);
        }

        // Set scrolling to false after scroll ends
        scrollTimeoutRef.current = setTimeout(() => {
            setIsScrolling(false);
        }, 150);
    }, [calculateProgress]);

    useEffect(() => {
        // Optimized scroll handler with RAF throttling
        const throttledHandleScroll = () => {
            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }

            rafRef.current = requestAnimationFrame(handleScroll);
        };

        // Get the content wrapper element
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');

        if (!contentWrapper) {
            console.warn('Content wrapper not found for scroll progress');
            return;
        }

        // Initial calculation
        handleScroll();

        // Add scroll listener to content wrapper instead of window
        contentWrapper.addEventListener('scroll', throttledHandleScroll, { passive: true });

        // Handle resize events that might affect scroll calculation
        const handleResize = () => {
            setTimeout(handleScroll, 100); // Delay to ensure layout is complete
        };
        window.addEventListener('resize', handleResize, { passive: true });

        return () => {
            contentWrapper.removeEventListener('scroll', throttledHandleScroll);
            window.removeEventListener('resize', handleResize);

            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
            if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current);
            }
        };
    }, [handleScroll]);

    const scrollToTop = useCallback(() => {
        // Get the content wrapper element
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');

        if (!contentWrapper) {
            // Fallback to window scroll
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            return;
        }

        // Scroll the content wrapper to top
        contentWrapper.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }, []);

    return (
        <>
            <div className={`scroll-progress-container ${isScrolling ? 'scrolling' : ''}`}>
                <div
                    className={`scroll-progress-bar ${scrollProgress > 0 ? 'active' : ''}`}
                    style={{
                        width: `${scrollProgress}%`,
                        opacity: scrollProgress > 0 ? 1 : 0
                    }}
                ></div>
            </div>
            {showButton && (
                <button
                    className="back-to-top hover-scale smooth-transition"
                    onClick={scrollToTop}
                    aria-label="Back to top"
                    style={{
                        opacity: showButton ? 1 : 0,
                        transform: showButton ? 'scale(1)' : 'scale(0.8)'
                    }}
                >
                    <FontAwesomeIcon icon={faArrowUp} />
                </button>
            )}
        </>
    );
};

export default ScrollProgress;
