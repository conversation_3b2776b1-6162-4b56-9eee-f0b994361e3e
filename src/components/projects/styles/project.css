@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 280px; /* Further reduced for maximum content density */
    height: 280px; /* Fixed height for consistency */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    padding: var(--spacing-sm) var(--spacing-lg); /* 0.5rem 1.5rem = 8px 24px - reduced vertical padding for content density */
    margin: 0 auto;
    overflow: hidden; /* Prevent content overflow */
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - minimal margin for maximum content space */
    flex-shrink: 0; /* Prevent header from shrinking */
    min-height: 40px; /* Reduced header height for content density */
}

.project-logo {
    width: 45px; /* Slightly reduced for better content balance */
    height: 45px;
    padding-right: var(--spacing-md); /* 1rem = 16px - reduced padding for content density */
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.project-title h3 {
    font-family: var(--secondary-font); /* Montserrat for headings */
    font-size: 1.6rem; /* Increased for better desktop readability */
    margin: 0;
    color: var(--primary-color);
    font-weight: var(--heading-weight); /* 600 */
    line-height: 1.2; /* Improved line height for readability */
    letter-spacing: -0.02em; /* Consistent with global h3 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Dark mode project styling */
.dark .project-title h3 {
    color: var(--dark-primary-text);
}

.dark .project-description {
    color: var(--dark-secondary-text);
}

.dark .project-content .subtitle {
    color: var(--dark-secondary-text);
}

.dark .project-description-list li {
    color: var(--dark-tertiary-text);
}

.dark .project-link {
    color: var(--dark-secondary-text);
}

.dark .project-link-text {
    color: var(--dark-secondary-text);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 var(--spacing-sm); /* 0.5rem = 8px */
    min-height: 0; /* Allow flex item to shrink */
    max-height: calc(100% - 60px); /* Ensure content doesn't overflow container */
    text-align: left;
    align-items: flex-start;
}


.project-description {
    color: var(--secondary-color);
}

/* Subtitle styling for project description title */
.project-content .subtitle {
    font-family: var(--primary-font); /* Poppins for body text */
    color: var(--secondary-color);
    font-size: 1.1rem; /* Increased for better desktop readability */
    font-weight: 500;
    margin: 0 0 var(--spacing-sm) 0; /* 0.5rem = 8px - increased margin */
    line-height: 1.3; /* Improved line height for readability */
    letter-spacing: 0.01em; /* Subtle letter spacing */
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: left; /* Ensure left alignment */
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    font-family: var(--primary-font); /* Poppins for body text */
    padding-left: var(--spacing-lg); /* 1.5rem = 24px */
    margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* 0.5rem = 8px - increased margins for better spacing */
    list-style-type: disc;
    overflow: visible; /* Remove scrolling */
    flex: 1;
    font-size: 1rem; /* Increased for better desktop readability */
    line-height: 1.5; /* Improved line height for better readability */
    height: auto; /* Allow natural height */
    text-align: left; /* Ensure left alignment */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-description-list::-webkit-scrollbar {
    display: none;
}

.project-description-list li {
    margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - increased for better readability */
    line-height: 1.5; /* Improved line height for better readability */
    position: relative;
    padding-left: var(--spacing-sm); /* 0.5rem = 8px - increased padding for better visual hierarchy */
    color: var(--secondary-color);
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    text-align: left; /* Ensure left alignment for list items */
}

/* Media query for large desktop screens - Maximum content density */
@media (min-width: 1200px) {
    .project {
        min-height: 260px; /* Ultra-compact for large screens */
        height: 260px;
        padding: var(--spacing-xs) var(--spacing-md); /* 0.25rem 1rem = 4px 16px - minimal padding for maximum content */
    }

    .project-header {
        margin-bottom: 2px; /* Minimal margin for maximum content space */
        min-height: 35px; /* Compact header for large screens */
    }

    .project-logo {
        width: 40px; /* Compact logo for content focus */
        height: 40px;
        padding-right: var(--spacing-sm); /* 0.5rem = 8px */
    }

    .project-title h3 {
        font-size: 1.7rem; /* Larger text for large screens */
        line-height: 1.1; /* Tight line height for space efficiency */
    }

    .project-content .subtitle {
        font-size: 1.15rem; /* Larger subtitle for large screens */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.2;
    }

    .project-description-list {
        font-size: 1.05rem; /* Larger description text for large screens */
        margin: var(--spacing-xs) 0 var(--spacing-xs) 0; /* Minimal margins */
        line-height: 1.4;
        max-height: calc(100% - 25px);
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.4;
    }
}

@media (max-width: 768px) {
    .project {
        min-height: 340px;
        height: 340px;
        padding: var(--spacing-md) var(--spacing-sm); /* 1rem 0.5rem = 16px 8px */
    }

    .project-title h3 {
        font-size: 1.3rem; /* Slightly reduced for better fit */
        line-height: 1.2; /* Tighter line height */
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Reduced for better fit */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.2; /* Tighter line height */
    }

    .project-description-list {
        font-size: 0.85rem; /* Reduced for better fit */
        padding-left: var(--spacing-md); /* 1rem = 16px */
        line-height: 1.3; /* Tighter line height */
        margin: var(--spacing-xs) 0 var(--spacing-xs) 0; /* Reduced margins */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.3; /* Consistent with parent */
    }
}

@media (max-width: 480px) {
    .project {
        min-height: 260px; /* Aggressively reduced height to fit content tightly */
        height: 260px;
        padding: var(--spacing-xs) var(--spacing-xs); /* 0.25rem 0.25rem = 4px 4px - minimal padding */
        overflow: visible; /* Allow content to be fully visible */
        box-sizing: border-box; /* Ensure padding is included in height calculations */
    }

    .project-header {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing for visual hierarchy */
        min-height: 42px; /* Balanced header height for readability */
        flex-shrink: 0; /* Prevent header from shrinking */
    }

    .project-logo {
        width: 36px; /* Restored size for better visual balance */
        height: 36px;
        padding-right: var(--spacing-md); /* 1rem = 16px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.2rem; /* Increased for better readability and hierarchy */
        line-height: 1.2; /* Balanced line height for readability */
        margin: 0; /* Remove default margins */
        word-wrap: break-word; /* Ensure long words wrap properly */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-content {
        padding: 0 var(--spacing-sm); /* 0.5rem = 8px - adequate padding for readability */
        flex: 1; /* Use all available space */
        overflow: hidden; /* Prevent content overflow */
        display: flex;
        flex-direction: column;
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Increased for better readability */
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
        line-height: 1.3; /* Improved line height for readability */
        word-wrap: break-word; /* Ensure text wraps properly */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-description-list {
        font-size: 0.85rem; /* Increased for better readability */
        padding-left: var(--spacing-lg); /* 1.5rem = 24px - proper indentation */
        margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* Balanced margins for visual hierarchy */
        line-height: 1.4; /* Improved line height for readability */
        height: auto; /* Allow natural height */
        overflow: visible; /* Remove scrolling */
        flex: 1; /* Take remaining space */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing between items */
        line-height: 1.4; /* Consistent with parent for readability */
        word-wrap: break-word; /* Ensure text wraps properly */
        overflow-wrap: break-word; /* Additional overflow protection */
        hyphens: auto; /* Enable hyphenation for better text wrapping */
    }
}

/* Intermediate mobile breakpoint for smoother transitions - Optimized for content density */
@media (max-width: 420px) {
    .project {
        min-height: 320px; /* Reduced height to remove empty space */
        height: 320px;
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px - reduced padding for more content space */
        overflow: visible; /* Allow content to be fully visible */
        box-sizing: border-box; /* Include padding in height calculations */
    }

    .project-header {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing for hierarchy */
        min-height: 40px; /* Balanced header height */
        flex-shrink: 0; /* Prevent header from shrinking */
    }

    .project-logo {
        width: 34px; /* Balanced size for visual hierarchy */
        height: 34px;
        padding-right: var(--spacing-md); /* 1rem = 16px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.15rem; /* Increased for better readability */
        line-height: 1.15; /* Balanced line height */
        margin: 0; /* Remove default margins */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-content {
        padding: 0 var(--spacing-sm); /* 0.5rem = 8px - adequate padding for readability */
        flex: 1; /* Use all available space */
        overflow: hidden; /* Prevent content overflow */
        display: flex;
        flex-direction: column;
    }

    .project-content .subtitle {
        font-size: 0.87rem; /* Increased for better readability */
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
        line-height: 1.25; /* Improved line height for readability */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-description-list {
        font-size: 0.82rem; /* Increased for better readability */
        padding-left: var(--spacing-md); /* 1rem = 16px - proper indentation */
        margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* Balanced margins for visual hierarchy */
        line-height: 1.37; /* Improved line height for readability */
        max-height: calc(100% - 22px); /* Ensure content fits within container */
        overflow-y: auto; /* Allow scrolling if needed */
        overflow-x: hidden; /* Prevent horizontal overflow */
        flex: 1; /* Take remaining space */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - balanced spacing */
        line-height: 1.37; /* Consistent with parent for readability */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
        hyphens: auto; /* Enable hyphenation for better text wrapping */
    }
}

@media (max-width: 380px) {
    .project {
        min-height: 310px; /* Reduced height to remove empty space */
        height: 310px;
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px - reduced padding for more content space */
        overflow: visible; /* Allow content to be fully visible */
        box-sizing: border-box; /* Include padding in height calculations */
    }

    .project-header {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing for hierarchy */
        min-height: 38px; /* Balanced header height for very small screens */
        flex-shrink: 0; /* Prevent header from shrinking */
    }

    .project-logo {
        width: 32px; /* Balanced size for visual hierarchy */
        height: 32px;
        padding-right: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.1rem; /* Increased for better readability */
        line-height: 1.15; /* Balanced line height for readability */
        margin: 0; /* Remove all default margins */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-content {
        padding: 0 var(--spacing-sm); /* 0.5rem = 8px - adequate padding for readability */
        flex: 1; /* Use all available space */
        overflow: hidden; /* Prevent content overflow */
        display: flex;
        flex-direction: column;
    }

    .project-content .subtitle {
        font-size: 0.85rem; /* Increased for better readability */
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
        line-height: 1.25; /* Improved line height for readability */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
    }

    .project-description-list {
        font-size: 0.8rem; /* Increased for better readability */
        padding-left: var(--spacing-md); /* 1rem = 16px - proper indentation */
        margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* Balanced margins for visual hierarchy */
        line-height: 1.35; /* Improved line height for readability */
        max-height: calc(100% - 20px); /* Ensure content fits within container */
        overflow-y: auto; /* Allow scrolling if content exceeds container */
        overflow-x: hidden; /* Prevent horizontal overflow */
        flex: 1; /* Take all remaining space */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - adequate spacing */
        line-height: 1.35; /* Consistent with parent for readability */
        word-wrap: break-word; /* Ensure proper text wrapping */
        overflow-wrap: break-word; /* Additional overflow protection */
        hyphens: auto; /* Enable hyphenation for better text wrapping */
    }
}
