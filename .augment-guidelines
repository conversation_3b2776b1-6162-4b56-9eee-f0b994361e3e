AI Augmentation Guidelines: Portfolio Website Development1. Master Plan: The PLANNING.md DocumentObjective: To establish a clear vision, strategy, and structure for the portfolio website before and during development.AI Responsibilities:Initial Consultation & Goal Definition:Engage with the user to understand the primary goals of the portfolio (e.g., job seeking, freelancing, showcasing specific skills, personal branding).Identify the target audience(s).Determine key skills, projects, and experiences to highlight.Discuss desired aesthetic, tone, and overall brand identity.Content Strategy:Outline the types of content needed (e.g., project descriptions, case studies, testimonials, blog posts, resume/CV).Plan the information architecture: Define sections (e.g., Home, About, Projects, Skills, Contact) and their hierarchy.Suggest compelling ways to present projects and skills.Technical & Design Considerations (Initial Thoughts):If applicable, discuss potential technologies or platforms (though this might be more for a developer AI, initial thoughts can be captured).Note any specific design preferences or inspirations provided by the user.Maintenance and Updates:Briefly outline a plan for future updates and content additions.PLANNING.md Structure (Suggested):# Portfolio Website Master Plan

## 1. Project Vision & Goals
    - Primary Objective:
    - Target Audience:
    - Key Message/Brand Identity:

## 2. Content Strategy
    - Core Sections:
        - Home: [Purpose, Key Elements]
        - About: [Key Information, Tone]
        - Projects: [Number of projects, Info per project]
        - Skills: [Categories, How to display]
        - Contact: [Methods, Call to Action]
        - (Optional Sections: Blog, Testimonials, Resume)
    - Content Inventory (High-Level):
        - [Project 1 Name]: [Brief Description, Assets needed]
        - [Project 2 Name]: [Brief Description, Assets needed]
        - ...
    - Voice and Tone:

## 3. Design & Technical Notes
    - Aesthetic Preferences:
    - Inspirations (Links/Examples):
    - Must-Have Features:
    - (Optional) Technology Stack Considerations:

## 4. Success Metrics
    - How will the success of the portfolio be measured? (e.g., inquiries, job offers, positive feedback)

## 5. Timeline & Milestones (High-Level)
    - Phase 1: Planning & Content Gathering
    - Phase 2: Design & Development
    - Phase 3: Review & Launch

## 6. Future Maintenance
    - Update Frequency:
    - Types of Updates:
Process:Create PLANNING.md: Initiate this document at the very beginning of the project.Iterative Refinement: Update PLANNING.md as discussions evolve, new ideas emerge, or requirements change. It's a living document.Reference Point: Continuously refer back to PLANNING.md to ensure all actions align with the overall strategy.2. Task Management: The TODO.md FileObjective: To break down the portfolio development process into actionable tasks, track progress, and ensure accountability.AI Responsibilities:Task Derivation: Based on PLANNING.md, generate a comprehensive list of tasks.Prioritization: Help the user prioritize tasks (e.g., Must-have, Should-have, Could-have).Status Tracking: Maintain the status of each task (e.g., To Do, In Progress, Blocked, Done).Assignment (if applicable): If multiple parties are involved, note who is responsible for each task.Deadline Setting (Optional): Assist in setting realistic deadlines.TODO.md Structure (Suggested - using Markdown Task Lists):# Portfolio Website TODO List

## Phase 1: Planning & Content Gathering
- [ ] Define primary goals (User) - `PLANNING.md`
- [ ] Identify target audience (User) - `PLANNING.md`
- [ ] List key projects to showcase (User)
- [ ] Write description for Project A (User)
- [ ] Gather assets (images, videos) for Project A (User)
- [ ] Draft 'About Me' section content (User/AI)
- [ ] Finalize `PLANNING.md` (AI/User)

## Phase 2: Design & Initial Setup
- [ ] Choose a color scheme (User/AI)
- [ ] Select typography (User/AI)
- [ ] Create wireframes/mockups for key pages (AI, if capable, or User)
- [ ] Set up project repository (AI/User)
- [ ] Implement homepage structure (AI/User)

## Phase 3: Content Implementation & Styling
- [ ] Add Project A content and images (AI/User)
- [ ] Style Project A page (AI/User)
- [ ] Implement 'About Me' page (AI/User)
- [ ] Style 'About Me' page (AI/User)
- [ ] Ensure responsive design (AI/User)

## Phase 4: Review & Launch
- [ ] Proofread all content (User/AI)
- [ ] Test all links and forms (AI/User)
- [ ] Check responsiveness on multiple devices (AI/User)
- [ ] Deploy website (AI/User)
- [ ] Announce launch (User)

## Backlog/Future Ideas
- [ ] Add a blog section
- [ ] Create animated project thumbnails
Process:Create TODO.md: After initial planning in PLANNING.md.Regular Updates: Review and update TODO.md daily or at the start/end of work sessions.Link to Planning: Ensure tasks in TODO.md directly support the goals outlined in PLANNING.md.3. Project Documentation: The README.md FileObjective: To provide essential information about the portfolio website project, making it understandable, maintainable, and shareable. While primarily for code projects, a simplified version is useful even for content-focused portfolio planning.AI Responsibilities:Project Overview: Write a concise summary of the portfolio website's purpose.Key Decisions Log (Optional but Recommended): Briefly note significant choices made during planning or design (e.g., "Decided to focus on 3 main projects for clarity," "Chose a minimalist design aesthetic").Content Structure Overview: Briefly describe the main sections and their purpose (can link to or summarize from PLANNING.md).How to Contribute/Edit (If applicable): If the user or others will be editing, provide simple instructions or conventions.Tools/Technologies Used (If applicable): List any specific tools, platforms, or frameworks being used (e.g., "Built with Hugo," "Hosted on Netlify," "Content managed via Markdown files").README.md Structure (Suggested):# [Your Name]'s Portfolio Website - Project Overview

## About This Portfolio
This repository/folder contains the planning and (eventually) content files for my personal portfolio website. The goal is to [reiterate main goal from PLANNING.md].

## Project Status
[e.g., Planning Phase, Content Gathering, Under Development, Live]

## Key Documents
* **`PLANNING.md`**: Contains the overall strategy, content plan, and design considerations.
* **`TODO.md`**: Tracks all actionable tasks and their progress.

## Content Structure
The website is planned to include the following main sections:
* Home
* About
* Projects
* Skills
* Contact

(Refer to `PLANNING.md` for more details.)

## (Optional) Key Decisions Log
* [Date]: Decided to [Decision Made] because [Reason].
* [Date]: Chose [Option] over [Alternative] for [Reason].

## (Optional) How to Update Content
* Project details are located in the `content/projects/` folder.
* To add a new project, create a new Markdown file in that directory.
* ...

## (Optional) Technologies Used
* Content Format: Markdown
* [Any other relevant tools or platforms]

---
*This README is a living document and will be updated as the project progresses.*
Process:Create README.md: Early in the project, alongside PLANNING.md.Maintain Relevance: Update README.md when significant structural changes occur, new tools are adopted, or the project status changes.Central Hub: Position README.md as the first point of entry for anyone (including the future self or the AI) trying to understand the project.General AI Conduct for Portfolio Editing:Proactive Suggestions: Based on the PLANNING.md and TODO.md, proactively suggest content creation, edits, or improvements.Clarity and Conciseness: Ensure all generated text (for the portfolio itself or for these documents) is clear, concise, and error-free.User Collaboration: Always work in collaboration with the user, seeking feedback and approval for significant changes or content generation.Version Control Awareness (if applicable): If version control (like Git) is used, remind the user to commit changes to these Markdown files regularly.Consistency: Maintain consistency in tone, style, and formatting across all documents and website content.Focus on User Goals: All planning, tasks, and documentation should ultimately serve the user's goals for their portfolio.By adhering to these guidelines, the AI can provide structured, efficient, and goal-oriented assistance in the planning and editing of a portfolio website.